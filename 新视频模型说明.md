# 新视频模型 wan2.2.5b 使用说明

## 概述

已成功添加新的视频生成模型 "视频生成模型服务器5 wan2.2.5b"，该模型使用新版本API (v2)，支持更高分辨率和更多参数。

## 新模型信息

- **模型名称**: 视频生成模型服务器5 wan2.2.5b
- **API地址**: https://innoai-wan-2-2-5b.hf.space/gradio_api/call/generate_video
- **API版本**: v2
- **模型索引**: 12 (在MODELS列表中)

## 新版本API特点

### 1. 数据格式
新版本API使用更简洁的数据格式：
```json
{
  "data": [
    "图片外链",           // 图片URL或base64数据
    "提示词",             // 视频描述
    "704*1280",          // 分辨率字符串
    3,                   // 时长，默认3秒
    30,                  // 采样步数，默认30
    5,                   // Scale，默认5
    5,                   // Sample Shift，默认5
    -1                   // Seed，-1表示随机
  ]
}
```

### 2. 支持的分辨率
- 704*1280 (竖屏)
- 1280*704 (横屏)

### 3. 默认参数
- 时长: 3秒
- 采样步数: 30 (比原版本的4步更高)
- Scale: 5
- Sample Shift: 5
- Seed: -1 (随机)

## 与原版本API的区别

| 特性 | 原版本API (v1) | 新版本API (v2) |
|------|----------------|----------------|
| 图片格式 | FileData对象 | 直接URL字符串 |
| 分辨率 | 分别传递width/height | 字符串格式 "width*height" |
| 默认步数 | 4 | 30 |
| 支持分辨率 | 512*896 | 704*1280, 1280*704 |
| 额外参数 | motion_bucket_id, cond_aug | Scale, Sample Shift |

## 前端界面变化

当选择新版本视频模型时，前端会自动：
1. 设置更高的默认分辨率 (704*1280)
2. 设置更高的默认步数 (30)
3. 显示 "使用新版本API (v2)" 的提示信息

## 代码实现

### 1. 模型配置
在 `MODELS` 列表中添加了新模型：
```python
{
    "display_name": "视频生成模型服务器5 wan2.2.5b",
    "source_url": "innoai-wan-2-2-5b.hf.space",
    "proxy_url": "",
    "type": "video",
    "api_version": "v2"  # 标记为新版本API
}
```

### 2. 生成函数
创建了新的生成函数 `generate_video_v2()` 来处理新版本API。

### 3. 路由处理
在 `/generate_video` 路由中根据模型的 `api_version` 自动选择使用哪个生成函数。

## 使用方法

1. 在模型选择下拉框中选择 "视频生成模型服务器5 wan2.2.5b"
2. 上传图片或提供图片链接
3. 输入视频描述提示词
4. 点击生成视频

系统会自动识别这是v2版本API并使用相应的参数格式。

## 注意事项

1. 新版本API优先支持图片外链，base64格式会尝试直接传递
2. 分辨率会根据选择的模型自动设置
3. 新版本API的生成时间可能与原版本不同
4. 所有视频模型共享相同的队列管理机制

## 测试状态

✅ 模型配置已添加
✅ API格式已实现
✅ 前端界面已更新
✅ 路由处理已完成
✅ 队列管理已集成
✅ 应用导入测试通过

## 完成的修改

### 1. 后端修改 (app.py)
- 在 `MODELS` 列表中添加了新的视频模型配置
- 创建了新的 `generate_video_v2()` 函数处理v2版本API
- 修改了 `/generate_video` 路由，根据API版本自动选择生成函数
- 保持了与原有视频模型的兼容性

### 2. 前端修改 (templates/index.html)
- 更新了模型选择选项，添加了 `data-api-version` 属性
- 修改了模型切换逻辑，根据API版本设置不同的默认参数
- 新版本模型会显示更高的默认分辨率和步数

### 3. 新增功能
- 支持新的API数据格式：`[图片外链, 提示词, 分辨率, 时长, 步数, Scale, Sample Shift, Seed]`
- 自动识别API版本并使用相应的处理逻辑
- 保持了完整的错误处理和重试机制
- 集成了现有的队列管理和积分系统

新视频模型已准备就绪，可以开始使用！

## 下一步建议

1. 在实际环境中测试新模型的视频生成功能
2. 根据实际使用情况调整默认参数
3. 监控新模型的性能和稳定性
4. 如需要，可以添加更多v2版本的视频模型
